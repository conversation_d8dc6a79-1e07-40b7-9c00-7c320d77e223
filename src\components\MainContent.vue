<template>
  <div class="main">
    <div class="main-title common-title">沈阳水务集团智能客服系统平台</div>
    <div class="main-body">
      <div class="main-three">
        <Mid @region-click="handleRegionClick" :region-data="regionData"></Mid>
      </div>
      <div class="main-footer">

      </div>
    </div>
    <div class="main-icon">
      <img src="../assets/img/charts-icon.png" alt="">
      <div class="charts-h-i">
        <img src="../assets/img/charts-h-icon.png" alt="">
        <div class="color-blue">{{ peak }}</div>
      </div>
    </div>
    <!-- 弹窗 -->
    <div v-if="isModal" class="dialog">
      <div class="dialog-bg" style="height: 813px;max-height: 813px;">
        <div class="d-t  flex space-between align-center">
          <div class="d-t-b"></div>
          <!-- <img class="" src="../assets/img/question.png" alt=""> -->
          <div class="dialog-title common-title" style="letter-spacing: 10px；">本年-客诉总量 {{ regionNames }}</div>
          <div class="d-t-c pointer" @click="closeD" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
            @mousedown="handleMouseDown" @mouseup="handleMouseUp">
            <img :src="currentImage" class="">
            <!-- 预加载所有状态图片 -->
            <img v-if="false" :src="normalImage" alt="预加载">
            <img v-if="false" :src="hoverImage" alt="预加载">
            <img v-if="false" :src="clickImage" alt="预加载">
          </div>
        </div>
        <!-- 内容 -->
        <div class="dialog-content margin-top">
          <el-row class="row-bg " :gutter="20">
            <el-col :span="4" v-for="(item, index) in dialogData" :key="item.type"
              class="flex space-between  q-d-b align-center pointer padding-top-xs margin-bottom-sm  "
              style="position: relative;" :class="{ 'active': dindex === index }"
              @mouseenter="handleDialogDataHover(item.issueSectionName, index)">

              <div class="text-left q-d-t">
                <div class="color-blue font-size-df">{{ item.issueSectionName }}</div>
                <div class="Roboto seats-title">
                  <span>{{ item.count }}</span>
                  <span class="font-size-sm color-54 ren margin-left-xs">件</span>
                </div>

              </div>
            </el-col>

          </el-row>

          <div class="complaint-table-container">
            <el-table :data="tableData" stripe highlight-current-row height="400px">
              <el-table-column type="index" label="序号" width="60"></el-table-column>

              <el-table-column prop="dispatchStatusName" label="状态" width="150">
                <template #default="scope">
                  <el-text :type="getStatusType(scope.row.dispatchStatusName)">{{
                    scope.row.dispatchStatusName }}</el-text>
                </template>
              </el-table-column>

              <el-table-column prop="billNo" label="客诉单号" width="150"></el-table-column>

              <el-table-column prop="occurAddress" label="发生地址" width="150"></el-table-column>

              <el-table-column prop="issueSectionName" label="问题板块" width="120"></el-table-column>

              <el-table-column prop="reportTypeName" label="反映类型" width="120"></el-table-column>

              <el-table-column prop="reflectionSourceName" label="反映来源" width="120"></el-table-column>

              <el-table-column prop="issueReasonName" label="问题原因" width="200">
                <template #default="scope">
                  <el-tooltip v-if="scope.row.issueReasonName" :content="scope.row.issueReasonName"
                    :disabled="scope.row.issueReasonName.length <= 20" placement="top" effect="customized"
                    :open-delay="300">
                    <!-- 显示的内容（截断+2行） -->
                    <div class="issue-description" v-if="scope.row.issueReasonName.length >= 20">
                      {{ formatIssueDesc(scope.row.issueReasonName) }}
                    </div>
                    <div v-else>{{ scope.row.issueReasonName }}</div>

                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="processingUnitName" label="处理单位" width="150"></el-table-column>

              <el-table-column prop="callNumber" label="来电号码" width="130"></el-table-column>

              <el-table-column prop="contactNumber" label="联系电话" width="130"></el-table-column>

              <el-table-column prop="acceptanceOpinion" label="回访结果" width="200">
                <template #default="scope">
                  <el-tooltip v-if="scope.row.acceptanceOpinion" :content="scope.row.acceptanceOpinion"
                    :disabled="scope.row.acceptanceOpinion.length <= 20" placement="top" effect="customized"
                    :open-delay="300">
                    <!-- 显示的内容（截断+2行） -->
                    <div class="issue-description" v-if="scope.row.acceptanceOpinion.length >= 20">
                      {{ formatIssueDesc(scope.row.acceptanceOpinion) }}
                    </div>
                    <div v-else>{{ scope.row.acceptanceOpinion }}</div>

                  </el-tooltip>
                </template>
              </el-table-column>

              <el-table-column prop="reflectionSourceName" label="回访方式" width="120">

              </el-table-column>
            </el-table>

            <!-- 分页控制 -->
            <el-pagination class="margin-top-sm" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-size="pageSize" layout="total,->,  pager,sizes, jumper,>-"
              :total="total">
            </el-pagination>
          </div>

        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, onUnmounted } from 'vue';
import { get, post } from '../utils/request'; // 假设这是您的HTTP请求工具

import normalImg from '../assets/img/close-btn.png'
import hoverImg from '../assets/img/close-hover.png'
import clickImg from '../assets/img/close-click.png'
import Mid from './MidComponent.vue'

export default {
  name: 'Main',
  components: {
    Mid
  },

  setup() {
    // 定时器ID，用于定时刷新数据
    let intervalId;

    // 控制弹窗显示/隐藏
    const isModal = ref(false);

    // 峰值数据（可能是最高客诉量）
    const peak = ref('');

    // 当前选中的区域名称
    const regionNames = ref('');

    // 当前选中的问题板块索引
    const dindex = ref(0);

    // 区域名称映射表，用于将系统中的区域名称转换为显示名称
    const regionNameMapping = {
      '康平县': '康平',
      '法库县': '法库',
      // '新民市': '新民',
      '辽中区': '辽中',
      '于洪区': '于洪',
      '沈抚新城': '沈抚新城',
      '大东区': '大东',
      '和平区': '和平',
      '苏家屯区': '苏家屯',
      '浑南区': '浑南',
      '沈河区': '沈河',
      '皇姑区': '皇姑',
      '铁西区': '铁西',
    }

    // 区域数据，包含各区域的客诉量和百分比
    const regionData = ref({
      '康平县': { name: '康平县', complaints: '37485', percentage: '40.32' },
      '法库县': { name: '法库县', complaints: '28756', percentage: '32.15' },
      // '新民市': { name: '新民市', complaints: '45123', percentage: '48.67' },
      '辽中区': { name: '辽中区', complaints: '31245', percentage: '35.89' },
      '于洪区': { name: '于洪区', complaints: '52341', percentage: '55.23' },
      '沈抚新城': { name: '沈抚新城', complaints: '29876', percentage: '33.45' },
      '大东区': { name: '大东区', complaints: '41567', percentage: '44.78' },
      '和平区': { name: '和平区', complaints: '38945', percentage: '42.11' },
      '苏家屯区': { name: '苏家屯区', complaints: '35678', percentage: '38.92' },
      '浑南区': { name: '浑南区', complaints: '47892', percentage: '51.34' },
      '沈河区': { name: '沈河区', complaints: '33456', percentage: '36.78' },
      '皇姑区': { name: '皇姑区', complaints: '39234', percentage: '43.21' },
      '铁西区': { name: '铁西区', complaints: '44567', percentage: '47.89' }
    })

    // 处理区域点击事件，显示对应区域的客诉详情弹窗
    const handleRegionClick = (regionName) => {
      isModal.value = true;
      regionNames.value = regionName;
      getMapsetionDetails(); // 获取区域详情数据
    };

    // 表格数据，显示客诉详情列表
    const tableData = ref([]);

    // 当前页码
    const currentPage = ref(1);

    // 每页显示数量
    const pageSize = ref(10);

    // 数据总条数
    const total = ref();

    // 根据客诉状态获取对应的Element UI文本类型
    const getStatusType = (status) => {
      const statusMap = {
        '满意': 'success',
        '理解': 'primary',
        '不满意': 'danger',
        '待回访': 'danger',
        '处理中': 'primary',
        '处理完成': 'success',
        '处理中（已发单）': 'primary',
        '登记': 'primary',
        '已上报': 'primary',
        '已回访': 'primary',
        '已分派': 'primary',
        '完工审核': 'success',
        '已结案': 'info',
        '暂存': 'info',
        '已核实': 'success',
        '已派遣': 'primary'
      };
      return statusMap[status] || 'default';
    };

    // 处理每页数量变化事件
    const handleSizeChange = (newSize) => {
      pageSize.value = newSize;
      currentPage.value = 1; // 重置到第一页
      getMapAreaDetails(); // 重新获取数据
    };

    // 处理页码变化事件
    const handleCurrentChange = (newPage) => {
      currentPage.value = newPage;
      getMapAreaDetails(); // 重新获取数据
    };

    // 弹窗关闭按钮的图片资源
    const normalImage = ref(normalImg);
    const hoverImage = ref(hoverImg);
    const clickImage = ref(clickImg);
    const currentImage = ref(normalImg);

    // 弹窗中的问题板块数据
    const dialogData = ref([]);

    // 当前选中的问题板块名称
    const sectionName = ref('');

    // 关闭按钮的鼠标事件处理
    const handleMouseEnter = () => {
      currentImage.value = hoverImage.value; // 鼠标悬停时切换图片
    };

    const handleMouseLeave = () => {
      currentImage.value = normalImage.value; // 鼠标离开时恢复原图
    };

    const handleMouseDown = () => {
      currentImage.value = clickImage.value; // 鼠标按下时切换图片
    };

    const handleMouseUp = (event) => {
      // 判断鼠标抬起时是否仍在按钮上
      const isMouseOver = event.relatedTarget?.closest('.d-t-c') !== null;
      currentImage.value = isMouseOver ? hoverImage.value : normalImage.value;
    };

    // 关闭弹窗
    const closeD = () => {
      isModal.value = false;
      currentImage.value = normalImage.value;
      dindex.value = 0;
    };

    // 组件挂载后执行初始化操作
    onMounted(() => {
      getMapsetion(); // 初始化获取地图板块数据
      intervalId = setInterval(getMapsetion, 5000); // 每5秒刷新一次数据
    });

    // 获取地图板块列表数据
    const getMapsetion = async () => {
      try {
        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/map-section');
        peak.value = response.data.maxCount; // 更新峰值数据

        // 处理返回的list数组，更新regionData
        if (response.data.list && Array.isArray(response.data.list)) {
          response.data.list.forEach(item => {
            const { count, percentage, processingUnitName } = item;

            // 使用regionNameMapping进行区域名称映射
            let regionKey = null;

            // 尝试通过映射表查找区域
            regionKey = Object.keys(regionNameMapping).find(key =>
              regionNameMapping[key] === processingUnitName
            );

            // 如果通过映射没找到，尝试直接匹配
            if (!regionKey) {
              regionKey = Object.keys(regionData.value).find(key => key === processingUnitName);
            }

            // 如果还是没找到，尝试processingUnitName作为完整区域名称
            if (!regionKey && Object.keys(regionData.value).includes(processingUnitName)) {
              regionKey = processingUnitName;
            }

            // 更新区域数据
            if (regionKey && regionData.value[regionKey]) {
              regionData.value[regionKey].complaints = count.toString();
              regionData.value[regionKey].percentage = percentage.toString();
            } else {
              console.warn('未找到匹配的区域:', processingUnitName, '可用区域:', Object.keys(regionData.value), '映射关系:', regionNameMapping);
            }
          });
        } else {
          console.warn('接口未返回list数组或list不是数组');
        }
      } catch (error) {
        console.error('获取地图板块失败', error);
      }
    };

    // 获取地图板块详情列表
    const getMapsetionDetails = async () => {
      try {
        const processingUnitName = regionNameMapping[regionNames.value] || regionNames.value;
        const data = {
          processingUnitName: processingUnitName
        };

        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/map-section/detail', data);
        dialogData.value = response.data; // 更新问题板块数据
        sectionName.value = response.data[0].issueSectionName; // 设置默认选中的问题板块
        getMapAreaDetails(); // 获取问题详情列表
      } catch (error) {
        console.error('获取地图板块-详情失败', error);
      }
    };

    // 处理问题板块悬停事件
    const handleDialogDataHover = (name, index) => {
      dindex.value = index; // 更新选中的问题板块索引
      sectionName.value = name; // 更新选中的问题板块名称
      getMapAreaDetails(); // 重新获取该问题板块的详情
    };

    // 获取问题详情列表（分页数据）
    const getMapAreaDetails = async () => {
      try {
        const processingUnitName = regionNameMapping[regionNames.value] || regionNames.value;
        const data = {
          issueSectionName: sectionName.value,
          pageNum: currentPage.value,
          pageSize: pageSize.value,
          processingUnitName: processingUnitName
        };

        const response = await get('/cloudcall-flowable-api/shenyang-report/screen/map-section/detail-list', data);
        tableData.value = response.data.content; // 更新表格数据
        total.value = response.data.totalElements; // 更新数据总数
      } catch (error) {
        console.error('获取地图板块-详情列表失败', error);
      }
    };

    // 组件卸载前清理定时器，防止内存泄漏
    onUnmounted(() => {
      clearInterval(intervalId);
    });

    // 格式化问题描述，限制显示长度
    const formatIssueDesc = (desc) => {
      if (!desc) return ''; // 处理空值
      // 限制最多20个字符（中文、英文均按1个字符计算）
      return desc.length > 20 ? desc.slice(0, 20) + '...' : desc;
    };

    // 导出变量和方法供模板使用
    return {
      isModal,
      dindex,
      regionData,
      peak,
      regionNames,
      // 弹框
      dialogData,
      currentImage,
      normalImage,
      hoverImage,
      clickImage,
      tableData,
      sectionName,
      currentPage,
      getMapAreaDetails,
      pageSize,
      handleDialogDataHover,
      getMapsetionDetails,
      total,
      handleRegionClick,
      getStatusType,
      handleSizeChange,
      handleCurrentChange,
      handleMouseEnter,
      handleMouseLeave,
      handleMouseDown,
      handleMouseUp,
      closeD,
      getMapsetion,
      formatIssueDesc
    };
  }
}
</script>

<style scoped>
.main {
  height: 100%;
}

.main-title {
  position: relative;
  width: 1440px;
  font-size: 44px;
  height: 52px;
}

.main-body {
  height: calc(100% - 52px);
}

.main-footer {
  height: 371px;
}

.main-three {
  height: calc(100% - 371px);
}

.main-icon {
  position: absolute;
  right: 50px;
  bottom: calc(371px + 50px);
  cursor: pointer;
}

.main-icon>img {
  width: 77px;
  height: 115px;
  position: relative;
}

.dialog-content {
  height: 680px;
}

.q-d-b {
  padding: 0 !important;
  background-position: left;
  background-size: 85% 121px;
}

.q-d-t {
  right: 90px;
}

.el-table .el-table__cell {
  border-bottom: 0 !important;
}

.dialog-content .seats-title span:first-child {
  color: #F4FDFF !important;
  opacity: 0.5;
}

.dialog-bg {
  background-image: url('../assets/img/m-d-b.png');
}
</style>
